import { RerankerOpenAI } from '../RerankerOpenAI.node';
import type { ISupplyDataFunctions } from 'n8n-workflow';

describe('RerankerOpenAI', () => {
	let rerankerOpenAI: RerankerOpenAI;
	let mockSupplyDataFunctions: Partial<ISupplyDataFunctions>;

	beforeEach(() => {
		rerankerOpenAI = new RerankerOpenAI();
		mockSupplyDataFunctions = {
			getNodeParameter: jest.fn(),
			getCredentials: jest.fn(),
			logger: {
				debug: jest.fn(),
				info: jest.fn(),
				warn: jest.fn(),
				error: jest.fn(),
			},
		};
	});

	describe('supplyData', () => {
		it('should return a reranker instance with correct configuration', async () => {
			// Mock the node parameters and credentials
			(mockSupplyDataFunctions.getNodeParameter as jest.Mock)
				.mockReturnValueOnce('custom-rerank-model') // modelName
				.mockReturnValueOnce(5); // topK

			(mockSupplyDataFunctions.getCredentials as jest.Mock).mockResolvedValue({
				apiKey: 'test-api-key',
				url: 'https://api.example.com',
				modelName: 'default-model',
			});

			const result = await rerankerOpenAI.supplyData.call(
				mockSupplyDataFunctions as ISupplyDataFunctions,
				0,
			);

			expect(result).toHaveProperty('response');
			expect(mockSupplyDataFunctions.getNodeParameter).toHaveBeenCalledWith('modelName', 0);
			expect(mockSupplyDataFunctions.getNodeParameter).toHaveBeenCalledWith('topK', 0, 10);
			expect(mockSupplyDataFunctions.getCredentials).toHaveBeenCalledWith('openAIApi');
		});

		it('should use default topK value when not provided', async () => {
			(mockSupplyDataFunctions.getNodeParameter as jest.Mock)
				.mockReturnValueOnce('test-model') // modelName
				.mockReturnValueOnce(undefined); // topK (should default to 10)

			(mockSupplyDataFunctions.getCredentials as jest.Mock).mockResolvedValue({
				apiKey: 'test-api-key',
				url: 'https://api.example.com',
				modelName: 'default-model',
			});

			const result = await rerankerOpenAI.supplyData.call(
				mockSupplyDataFunctions as ISupplyDataFunctions,
				0,
			);

			expect(result).toHaveProperty('response');
			expect(mockSupplyDataFunctions.getNodeParameter).toHaveBeenCalledWith('topK', 0, 10);
		});

		it('should fall back to credentials model name when node parameter is empty', async () => {
			(mockSupplyDataFunctions.getNodeParameter as jest.Mock)
				.mockReturnValueOnce('') // empty modelName
				.mockReturnValueOnce(10); // topK

			(mockSupplyDataFunctions.getCredentials as jest.Mock).mockResolvedValue({
				apiKey: 'test-api-key',
				url: 'https://api.example.com',
				modelName: 'credentials-model',
			});

			const result = await rerankerOpenAI.supplyData.call(
				mockSupplyDataFunctions as ISupplyDataFunctions,
				0,
			);

			expect(result).toHaveProperty('response');
		});
	});

	describe('node description', () => {
		it('should have correct node properties', () => {
			const { description } = rerankerOpenAI;

			expect(description.displayName).toBe('Reranker OpenAI');
			expect(description.name).toBe('rerankerOpenAI');
			expect(description.group).toEqual(['transform']);
			expect(description.version).toBe(1);
			expect(description.credentials).toEqual([
				{
					name: 'openAIApi',
					required: true,
				},
			]);
		});

		it('should have correct input/output configuration', () => {
			const { description } = rerankerOpenAI;

			expect(description.inputs).toEqual([]);
			expect(description.outputs).toEqual(['ai_reranker']);
			expect(description.outputNames).toEqual(['Reranker']);
		});

		it('should have correct properties configuration', () => {
			const { description } = rerankerOpenAI;

			expect(description.properties).toHaveLength(2);
			
			const modelProperty = description.properties.find(p => p.name === 'modelName');
			expect(modelProperty).toBeDefined();
			expect(modelProperty?.type).toBe('string');
			expect(modelProperty?.default).toBe('={{ $credentials.modelName }}');

			const topKProperty = description.properties.find(p => p.name === 'topK');
			expect(topKProperty).toBeDefined();
			expect(topKProperty?.type).toBe('number');
			expect(topKProperty?.default).toBe(10);
		});
	});
});
