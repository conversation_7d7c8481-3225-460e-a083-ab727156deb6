import type {
	IAuthenticateGeneric,
	ICredentialTestRequest,
	ICredentialType,
	INodeProperties,
} from 'n8n-workflow';

export class OpenAIApi implements ICredentialType {
	name = 'openAIApi';
	displayName = 'OpenAI API';
	documentationUrl = 'openai';
	properties: INodeProperties[] = [
		{
			displayName: 'API Key',
			name: 'apiKey',
			type: 'string',
			typeOptions: {
				password: true,
			},
			required: true,
			default: '',
		},
		{
			displayName: 'Base URL',
			name: 'url',
			type: 'string',
			description: 'Base URL for the OpenAI-compatible API endpoint',
			default: 'https://api.openai.com',
			placeholder: 'https://api.openai.com',
		},
		{
			displayName: 'Model Name',
			name: 'modelName',
			type: 'string',
			description: 'Name of the reranker model to use',
			default: 'rerank-1',
			placeholder: 'rerank-1',
		},
	];

	authenticate: IAuthenticateGeneric = {
		type: 'generic',
		properties: {
			headers: {
				Authorization: '=Bearer {{$credentials.apiKey}}',
			},
		},
	};

	test: ICredentialTestRequest = {
		request: {
			baseURL: '={{ $credentials.url }}',
			url: '/v1/models',
		},
	};
}
